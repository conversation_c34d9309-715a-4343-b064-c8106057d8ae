accessWidener	v2	named

accessible   class   net/minecraft/client/texture/PlayerSkinProvider$FileCache
accessible   class   net/minecraft/client/particle/FireworksSparkParticle$Explosion
accessible   class   net/minecraft/network/packet/c2s/play/PlayerInteractEntityC2SPacket$InteractType
accessible   class   net/minecraft/network/packet/c2s/play/PlayerInteractEntityC2SPacket$InteractAtHandler
accessible   class   net/minecraft/network/packet/c2s/play/PlayerInteractEntityC2SPacket$InteractTypeHandler
accessible   class   net/minecraft/client/world/ClientChunkManager$ClientChunkMap

accessible   class   net/minecraft/client/texture/MapTextureManager$MapTexture
accessible   field   net/minecraft/client/texture/MapTextureManager$MapTexture texture Lnet/minecraft/client/texture/NativeImageBackedTexture;

accessible   class   net/minecraft/client/gui/screen/ingame/BeaconScreen$EffectButtonWidget

accessible   class   net/minecraft/client/resource/ResourceReloadLogger$ReloadState

accessible   class   com/mojang/blaze3d/opengl/GlStateManager$CapabilityTracker
accessible   method  com/mojang/blaze3d/pipeline/RenderPipeline$Builder <init> ()V
accessible   method   com/mojang/blaze3d/pipeline/RenderPipeline$Builder withSnippet (Lcom/mojang/blaze3d/pipeline/RenderPipeline$Snippet;)V

# Auto Fish
accessible class net/minecraft/entity/projectile/FishingBobberEntity$State
accessible field net/minecraft/entity/projectile/FishingBobberEntity state Lnet/minecraft/entity/projectile/FishingBobberEntity$State;
