name: Suggestion
description: Suggest a new feature or change for Meteor.
labels: [ enhancement ]
body:
  - type: textarea
    id: description
    attributes:
      label: Describe the feature
      description: A clear and concise description of what the feature/change is.
    validations:
      required: true
  - type: checkboxes
    id: prerequisites
    attributes:
      label: Before submitting a suggestion
      options:
        - label: |
            This feature doesn't already exist in the client. (I have checked every module and their settings on the **latest dev build**)
          required: true
        - label: |
            This wasn't already suggested. (I have searched suggestions on GitHub and read the FAQ)
          required: true
