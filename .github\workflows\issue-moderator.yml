name: Issue Automatic Actions

on:
  issues:
    types: [ opened ]

jobs:
  issue-moderator:
    runs-on: ubuntu-latest

    permissions:
      issues: write
      contents: read

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Set Up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Install Dependencies
        working-directory: .github/moderation
        run: npm ci

      - name: Check Banned Terms
        run: node .github/moderation/banned_terms.js
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
