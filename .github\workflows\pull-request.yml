name: Build Pull Request

on: [ pull_request ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Set up Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Set up Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '21'

      - name: Build
        run: ./gradlew build

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: pull-request-build
          path: build/libs/
