name: Bug
description: Report a bug to help us improve Meteor.
labels: [ bug ]
body:
  - type: textarea
    id: description
    attributes:
      label: Describe the bug
      description: |
        A clear and concise description of what the issue is.
        Provide as much information as possible, videos, images, etc.
      placeholder: |
          This module is broken / not working as intended.
          It should do X but it does Y instead.
    validations:
      required: true
  - type: textarea
    id: reproducing
    attributes:
      label: Steps to reproduce
      description: How do you trigger this bug?
      placeholder: |
          1. I did this thing;
          2. Then I did this other thing, which caused the bug.
    validations:
      required: true
  - type: input
    id: meteor-version
    attributes:
      label: Meteor Version
      placeholder: Meteor X.Y.Z (or X.Y.Z-build_number)
    validations:
      required: true
  - type: input
    id: mc-version
    attributes:
      label: Minecraft Version
      placeholder: MC X.Y.Z
    validations:
      required: true
  - type: dropdown
    id: operating-systems
    attributes:
      label: Operating System
      options:
        - macOS
        - Windows
        - Linux
    validations:
      required: true
  - type: checkboxes
    id: prerequisites
    attributes:
      label: Before submitting a bug report
      options:
        - label: |
            This bug wasn't already reported (I have searched bug reports on GitHub).
          required: true
        - label: |
            This is a valid bug (I am able to reproduce this on the latest dev build).
          required: true
