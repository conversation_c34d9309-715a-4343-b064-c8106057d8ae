{"required": true, "package": "meteordevelopment.meteorclient.mixin", "compatibilityLevel": "JAVA_21", "plugin": "meteordevelopment.meteorclient.MixinPlugin", "client": ["AbstractBlockAccessor", "AbstractBlockMixin", "AbstractBlockStateMixin", "AbstractBoatEntityMixin", "AbstractClientPlayerEntityAccessor", "AbstractClientPlayerEntityMixin", "AbstractFurnaceScreenHandlerMixin", "AbstractFurnaceScreenMixin", "AbstractSignBlockEntityRendererMixin", "AbstractSignEditScreenAccessor", "AbstractSignEditScreenMixin", "ArmorFeatureRendererMixin", "BakedQuadMixin", "BannerBlockEntityRendererMixin", "BeaconBlockEntityRendererMixin", "BeaconScreenMixin", "BiomeColorsMixin", "BlockCollisionSpliteratorMixin", "BlockColorsMixin", "BlockEntityRenderDispatcherMixin", "BlockHitResultAccessor", "BlockItemMixin", "BlockMixin", "BlockModelRendererMixin", "BlockRenderManagerMixin", "BlockStateMixin", "BookEditScreenMixin", "BookScreenMixin", "BossBarHudMixin", "BoxMixin", "BrewingStandScreenMixin", "BrightnessGetterMixin", "CameraMixin", "CapabilityTrackerMixin", "CapeFeatureRendererMixin", "ChatHudAccessor", "ChatHudLineMixin", "ChatHudLineVisibleMixin", "ChatHudMixin", "ChatInputSuggestorMixin", "ChatScreenMixin", "ChunkAccessor", "ChunkBorderDebugRendererMixin", "ChunkOcclusionDataBuilderMixin", "ChunkSkyLightProviderMixin", "ClientChunkManagerAccessor", "ClientChunkMapAccessor", "ClientConnectionAccessor", "ClientConnectionMixin", "ClientPlayerEntityAccessor", "ClientPlayerEntityMixin", "ClientPlayerInteractionManagerAccessor", "ClientPlayerInteractionManagerMixin", "ClientPlayNetworkHandlerAccessor", "ClientPlayNetworkHandlerMixin", "ClientWorldMixin", "CobwebBlockMixin", "CompassStateMixin", "ConnectScreenMixin", "ContainerComponentAccessor", "ContainerComponentMixin", "CrashReportMixin", "CreativeInventoryScreenAccessor", "CreativeSlotMixin", "CrossbowItemAccessor", "DefaultSkinHelperMixin", "DirectionAccessor", "DisconnectedScreenMixin", "DrawContextMixin", "ElytraFeatureRendererMixin", "EnchantingTableBlockEntityRendererMixin", "EndCrystalEntityModelMixin", "EndCrystalEntityRendererMixin", "EntityAccessor", "EntityBucketItemAccessor", "EntityMixin", "EntityRenderDispatcherMixin", "EntityRendererMixin", "EntityRenderStateMixin", "EntityTrackingSectionAccessor", "EntityVelocityUpdateS2CPacketAccessor", "ExplosionS2CPacketMixin", "FileCacheAccessor", "FireworkRocketEntityMixin", "FireworksSparkParticleMixin", "FireworksSparkParticleSubMixin", "FishingBobberEntityAccessor", "FishingBobberEntityMixin", "FluidRendererMixin", "Fog<PERSON>endererMixin", "GameOptionsMixin", "GameRendererMixin", "GlBackendMixin", "GlCommandEncoderMixin", "GpuTextureMixin", "HandledScreenAccessor", "HandledScreenMixin", "HeadFeatureRendererMixin", "HeldItemRendererMixin", "HorseScreenHandlerAccessor", "InGameHudMixin", "InGameOverlayRendererMixin", "ItemEntityRendererMixin", "ItemGroupsAccessor", "ItemGroupsMixin", "ItemMixin", "ItemRendererMixin", "ItemRenderStateAccessor", "ItemStackMixin", "KeyBindingAccessor", "KeyboardInputMixin", "KeyboardMixin", "LayerRenderStateAccessor", "LightmapTextureManagerMixin", "LightningEntityRendererMixin", "LivingEntityAccessor", "LivingEntityMixin", "LivingEntityRendererMixin", "MapRendererMixin", "MapTextureManagerAccessor", "MessageHandlerMixin", "MinecraftClientAccessor", "MinecraftClientMixin", "MinecraftServerAccessor", "MobEntityMixin", "MobSpawnerBlockEntityRendererMixin", "MouseMixin", "MultiPhaseMixin", "MultiPhaseParametersMixin", "MultiplayerScreenMixin", "MutableTextMixin", "PacketByteBufMixin", "ParticleManagerMixin", "PlayerEntityAccessor", "PlayerEntityMixin", "PlayerEntityRendererMixin", "PlayerInteractEntityC2SPacketMixin", "PlayerListEntryMixin", "PlayerListHudMixin", "PlayerMoveC2SPacketAccessor", "PlayerMoveC2SPacketMixin", "PlayerSkinProviderAccessor", "PowderSnowBlockMixin", "ProjectileInGroundAccessor", "ProjectionMatrix2Accessor", "RaycastContextMixin", "RegistriesMixin", "ReloadStateAccessor", "RenderLayersMixin", "RenderPipelineMixin", "RenderSystemMixin", "RenderTickCounterDynamicMixin", "ResourceReloadLoggerAccessor", "ScreenMixin", "SectionedEntityCacheAccessor", "ServerPlayerEntityMixin", "ServerResourcePackLoaderMixin", "ShaderLoaderMixin", "ShulkerBoxScreenHandlerAccessor", "SimpleEntityLookupAccessor", "SimpleOptionMixin", "SlimeBlockMixin", "SlotMixin", "SoundSystemMixin", "SplashTextResourceSupplierMixin", "StatusEffectFogModifierMixin", "StatusEffectInstanceAccessor", "StatusEffectInstanceMixin", "StringHelperMixin", "SweetBerryBushBlockMixin", "TextHandlerAccessor", "TextMixin", "TextRendererMixin", "TextVisitFactoryMixin", "TitleScreenMixin", "TooltipComponentMixin", "TooltipDisplayComponentMixin", "TransformationMixin", "TridentItemMixin", "Vec3dMixin", "WorldAccessor", "WorldBorderMixin", "WorldChunkMixin", "WorldRendererAccessor", "WorldRendererMixin", "YggdrasilMinecraftSessionServiceAccessor"], "injectors": {"defaultRequire": 1}}