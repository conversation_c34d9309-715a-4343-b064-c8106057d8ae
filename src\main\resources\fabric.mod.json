{"schemaVersion": 1, "name": "Meteor Client", "id": "meteor-client", "version": "${version}", "description": "Based utility mod.", "authors": ["MineGame159", "squidoodly", "seasnail"], "contact": {"homepage": "https://meteorclient.com", "issues": "https://github.com/MeteorDevelopment/meteor-client/issues", "sources": "https://github.com/MeteorDevelopment/meteor-client", "discord": "https://meteorclient.com/discord"}, "license": "GPL-3.0", "icon": "assets/meteor-client/icon.png", "environment": "client", "entrypoints": {"client": ["meteordevelopment.meteorclient.MeteorClient"], "modmenu": ["meteordevelopment.meteorclient.ModMenuIntegration"]}, "mixins": ["meteor-client.mixins.json", "meteor-client-baritone.mixins.json", "meteor-client-indigo.mixins.json", "meteor-client-sodium.mixins.json", "meteor-client-lithium.mixins.json", "meteor-client-viafabricplus.mixins.json"], "accessWidener": "meteor-client.accesswidener", "custom": {"meteor-client:build_number": "${build_number}", "meteor-client:color": "145,61,226", "meteor-client:commit": "${commit}", "modmenu": {"links": {"modmenu.discord": "https://meteorclient.com/discord"}}}, "depends": {"java": ">=21", "minecraft": ["${minecraft_version}", "1.21.7", "1.21.6"], "fabricloader": ">=${loader_version}"}, "breaks": {"optifabric": "*", "feather": "*", "origins": "*", "sodium": "<0.6.12", "morechathistory": "*"}}