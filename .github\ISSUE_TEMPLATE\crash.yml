name: Crash
description: Report a crash to help us improve Meteor.
labels: [ crash ]
body:
  - type: textarea
    id: reproducing
    attributes:
      label: Steps to reproduce
      description: How do you trigger this crash?
      placeholder: |
          1. I did this thing;
          2. Then I did this other thing, which caused the crash.
    validations:
      required: true
  - type: input
    id: crash-report
    attributes:
      label: Link to crash report/log (upload to https://mclo.gs and paste resulting link here)
      placeholder: https://mclo.gs/xxxxxx
    validations:
        required: true
  - type: input
    id: meteor-version
    attributes:
      label: Meteor Version
      placeholder: Meteor X.Y.Z (or X.Y.Z-build_number)
    validations:
      required: true
  - type: input
    id: mc-version
    attributes:
      label: Minecraft Version
      placeholder: MC X.Y.Z
    validations:
      required: true
  - type: dropdown
    id: operating-systems
    attributes:
      label: Operating System
      options:
        - macOS
        - Windows
        - Linux
    validations:
      required: true
  - type: checkboxes
    id: prerequisites
    attributes:
      label: Before submitting a crash report
      options:
        - label: |
            This crash wasn't already reported (I have searched crash reports on GitHub).
          required: true
        - label: |
            This is a valid crash (I am able to reproduce this on the latest dev build).
          required: true
